export enum CQuotationItemType {
  /** 客變需求 */
  客變需求 = 1,
  /** 自定義 */
  自定義 = 2,
  /** 選樣 */
  選樣 = 3
}

export interface QuotationItem {
  cQuotationID?: number;
  cHouseID: number;
  cItemName: string;
  cLocation?: string;
  cUnit?: string;
  cUnitPrice: number;
  cCount: number;
  cStatus?: number;
  CQuotationItemType: CQuotationItemType;
  cRemark?: string;
}

export interface QuotationRequest {
  houseId: number;
  items: QuotationItem[];
}

export interface QuotationResponse {
  success: boolean;
  message: string;
  data?: QuotationItem[];
}
