/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { apiFileGetFileGet } from '../fn/file/api-file-get-file-get';
import { ApiFileGetFileGet$Params } from '../fn/file/api-file-get-file-get';

@Injectable({ providedIn: 'root' })
export class FileService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `apiFileGetFileGet()` */
  static readonly ApiFileGetFileGetPath = '/api/File/GetFile';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiFileGetFileGet()` instead.
   *
   * This method doesn't expect any request body.
   */
  apiFileGetFileGet$Response(params?: ApiFileGetFileGet$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return apiFileGetFileGet(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiFileGetFileGet$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  apiFileGetFileGet(params?: ApiFileGetFileGet$Params, context?: HttpContext): Observable<void> {
    return this.apiFileGetFileGet$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

}
