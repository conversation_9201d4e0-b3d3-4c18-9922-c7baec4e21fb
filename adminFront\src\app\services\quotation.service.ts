import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { QuotationItem, QuotationRequest, QuotationResponse, CQuotationItemType } from '../models/quotation.model';
import { QuotationService as ApiQuotationService } from '../../services/api/services/quotation.service';
import { EnumQuotationItemType } from '../../services/api/models/enum-quotation-item-type';
import {
  GetListQuotationRequest,
  GetQuotationByIdRequest,
  SaveDataQuotation,
  QuotationItemModel,
  GetListByHouseIdRequest,
  LoadDefaultItemsRequest
} from '../../services/api/models';

@Injectable({
  providedIn: 'root'
})
export class QuotationService {
  private readonly apiUrl = '/api/Quotation';

  constructor(
    private apiQuotationService: ApiQuotationService,
    private http: HttpClient
  ) { }

  // 將 EnumQuotationItemType 轉換為 CQuotationItemType
  private mapEnumToCQuotationType(enumType?: EnumQuotationItemType): CQuotationItemType {
    switch (enumType) {
      case EnumQuotationItemType.$1:
        return CQuotationItemType.客變需求;
      case EnumQuotationItemType.$3:
        return CQuotationItemType.選樣;
      case EnumQuotationItemType.$2:
      default:
        return CQuotationItemType.自定義;
    }
  }
  // 取得報價單列表
  getQuotationList(request: GetListQuotationRequest): Observable<any> {
    return this.apiQuotationService.apiQuotationGetListPost$Json({ body: request });
  }
  // 取得單筆報價單資料
  getQuotationData(quotationId: number): Observable<any> {
    const request: GetQuotationByIdRequest = { CQuotationID: quotationId };
    return this.apiQuotationService.apiQuotationGetDataPost$Json({ body: request });
  }  // 取得戶別的報價項目
  getQuotationByHouseId(houseId: number): Observable<any> {
    const request: GetListByHouseIdRequest = { CHouseID: houseId };
    return this.apiQuotationService.apiQuotationGetListByHouseIdPost$Json({ body: request }).pipe(
      map(response => {
        // 將 API 響應的小寫屬性名轉換為前端期望的大寫屬性名
        if (response) {
          return {
            StatusCode: response.StatusCode,
            Message: response.Message,
            TotalItems: response.TotalItems,
            Entries: response.Entries // 保持原始結構，因為 entries 是一個物件而不是陣列
          };
        }
        return response;
      })
    );
  }
  // 儲存報價單 (支援單一項目)
  saveQuotationItem(quotation: SaveDataQuotation): Observable<any> {
    return this.apiQuotationService.apiQuotationSaveDataPost$Json({ body: quotation });
  }  // 儲存報價單 (支援批量保存多個項目) - 使用單一請求
  saveQuotation(request: {
    houseId: number;
    items: QuotationItem[];
    quotationId?: number;
    cShowOther?: boolean;
    cOtherName?: string;
    cOtherPercent?: number;
  }): Observable<QuotationResponse> {
    // 將 QuotationItem 轉換為 QuotationItemModel 格式
    const quotationItems: QuotationItemModel[] = request.items.map(item => {
      const quotationType = item.CQuotationItemType && item.CQuotationItemType > 0 ? item.CQuotationItemType : CQuotationItemType.自定義;


      return {
        CItemName: item.cItemName,
        CLocation: item.cLocation || '',
        CUnit: item.cUnit || '',
        CUnitPrice: item.cUnitPrice,
        CCount: item.cCount,
        CStatus: item.cStatus || 1,
        CQuotationItemType: quotationType,
        CRemark: item.cRemark || ''
      };
    });

    // 建立 SaveDataQuotation 請求，並直接添加額外費用欄位
    const saveRequest: any = {
      CHouseID: request.houseId,
      CQuotationVersionId: request.quotationId || 0, // 使用傳入的 quotationId，如果沒有則為 0（新建報價單）
      Items: quotationItems,
      // 額外費用相關欄位 - 直接添加到請求中
      CShowOther: request.cShowOther || false,
      COtherName: request.cOtherName || '',
      COtherPercent: request.cOtherPercent || 0
    };

    // 調試用 - 印出最終送出的請求資料
    console.log('QuotationService saveRequest:', saveRequest);

    return this.apiQuotationService.apiQuotationSaveDataPost$Json({ body: saveRequest }).pipe(
      map(response => ({
        success: response?.StatusCode === 0,
        message: response?.StatusCode === 0 ? '報價單保存成功' : '報價單保存失敗',
        data: request.items
      } as QuotationResponse))
    );
  }

  // 取得預設報價項目 (保持原有方法以兼容現有代碼)
  getDefaultQuotationItems(): Observable<QuotationResponse> {
    // 使用 GetList 方法獲取預設項目
    const request: GetListQuotationRequest = {
      PageIndex: 0,
      PageSize: 100,
      // 其他預設參數可能需要根據實際 API 需求調整
    };
    return this.apiQuotationService.apiQuotationGetListPost$Json({ body: request }).pipe(
      map(response => {
        // 將 GetQuotation 轉換為 QuotationItem
        const quotationItems: QuotationItem[] = (response.Entries || []).map(item => ({
          cHouseID: item.CHouseID || 0,
          cItemName: item.CItemName || '',
          cUnit: item.CUnit || '',
          cUnitPrice: item.CUnitPrice || 0,
          cCount: item.CCount || 0,
          cStatus: item.CStatus || 1,
          CQuotationItemType: this.mapEnumToCQuotationType(item.CQuotationItemType),
          cRemark: item.CRemark || ''
        }));

        // 轉換 API 響應格式以保持兼容性
        return {
          success: response.StatusCode === 0, // 假設 statusCode 0 表示成功
          message: response.Message || '',
          data: quotationItems
        } as QuotationResponse;
      })
    );
  }  // 載入預設報價項目 (LoadDefaultItems API)
  loadDefaultItems(request: LoadDefaultItemsRequest): Observable<QuotationResponse> {
    return this.apiQuotationService.apiQuotationLoadDefaultItemsPost$Json({ body: request }).pipe(
      map(response => {
        // 將 GetQuotation 轉換為 QuotationItem
        const quotationItems: QuotationItem[] = (response.Entries || []).map(item => ({
          cHouseID: item.CHouseID || 0,
          cItemName: item.CItemName || '',
          cUnit: item.CUnit || '',
          cUnitPrice: item.CUnitPrice || 0,
          cCount: item.CCount || 0,
          cStatus: item.CStatus || 1,
          CQuotationItemType: this.mapEnumToCQuotationType(item.CQuotationItemType),
          cRemark: item.CRemark || ''
        }));

        // 轉換 API 響應格式以保持兼容性
        return {
          success: response.StatusCode === 0, // 假設 StatusCode 0 表示成功
          message: response.Message || '',
          data: quotationItems
        } as QuotationResponse;
      })
    );
  }  // 載入常規報價項目 (LoadRegularItems API)
  loadRegularItems(request: LoadDefaultItemsRequest): Observable<QuotationResponse> {
    return this.apiQuotationService.apiQuotationLoadRegularItemsPost$Json({ body: request }).pipe(
      map(response => {
        // 將 GetQuotation 轉換為 QuotationItem
        const quotationItems: QuotationItem[] = (response.Entries || []).map(item => ({
          cHouseID: item.CHouseID || 0,
          cItemName: item.CItemName || '',
          cUnit: item.CUnit || '',
          cUnitPrice: item.CUnitPrice || 0,
          cCount: item.CCount || 0,
          cStatus: item.CStatus || 1,
          CQuotationItemType: this.mapEnumToCQuotationType(item.CQuotationItemType),
          cRemark: item.CRemark || ''
        }));

        // 轉換 API 響應格式以保持兼容性
        return {
          success: response.StatusCode === 0, // 假設 StatusCode 0 表示成功
          message: response.Message || '',
          data: quotationItems
        } as QuotationResponse;
      })
    );
  }

  // 更新報價項目 (使用 SaveData 方法)
  updateQuotationItem(quotationId: number, item: QuotationItem): Observable<QuotationResponse> {
    const saveData: SaveDataQuotation = {
      CHouseID: item.cHouseID,
      CQuotationVersionId: quotationId,
      Items: [{
        CItemName: item.cItemName,
        CLocation: item.cLocation || '',
        CUnitPrice: item.cUnitPrice,
        CCount: item.cCount,
        CStatus: item.cStatus || 1,
        CQuotationItemType: item.CQuotationItemType || CQuotationItemType.自定義
      }]
    };
    return this.apiQuotationService.apiQuotationSaveDataPost$Json({ body: saveData }).pipe(
      map(response => {
        return {
          success: response.StatusCode === 0, // 假設 StatusCode 0 表示成功
          message: response.Message || '',
          data: [item] // 包裝為陣列以符合 QuotationResponse 格式
        } as QuotationResponse;
      })
    );
  }

  // 匯出報價單 (需要另外實作，因為 swagger 中沒有此 API)
  exportQuotation(houseId: number): Observable<Blob> {
    // 這個方法可能需要使用其他 API 或保持原有實作
    // 暫時拋出錯誤提示需要實作
    throw new Error('Export quotation functionality needs to be implemented separately');
  }

  // 鎖定報價單
  lockQuotation(quotationId: number): Observable<any> {
    return this.apiQuotationService.apiQuotationLockQuotationPost$Json({ body: quotationId }).pipe(
      map(response => {
        return {
          success: response.StatusCode === 0,
          message: response.Message || '',
          data: response.Entries || null
        };
      })
    );
  }
}
